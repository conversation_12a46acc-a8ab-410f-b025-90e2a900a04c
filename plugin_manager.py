# plugin_manager.py

import importlib.util
import os
import sys
from case_base import *
from PyQt5.QtCore import  QThread
import threading
import traceback

class PluginThread(QThread):
    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin
        self.stop_flag = threading.Event()
        self.running = False

    def run(self):
        try:
            self.plugin.run(stop_flag=self.stop_flag)  # 启动插件的任务
            self.running = True
        except Exception as e:
            tb_str = traceback.format_exc()  # This will include file name, line number, and function name
            print(f"Error in plugin execution: {e}")
            print("Detailed traceback:")
            print(tb_str)
            self.running = False

    def stop(self):
        self.stop_flag.set()
        self.quit()  # 终止线程的执行
        self.wait()  # 等待线程完全结束
        self.running = False

class PluginManager():
    def __init__(self, plugin_dir='plugins'):
        super().__init__()
        self.plugin_dir = plugin_dir
        self.plugins = {}
        self.plugin_threads = {}

    def load_plugin(self, plugin_name):
        plugin_path = os.path.join(self.plugin_dir, f"{plugin_name}.py")
        if "case_base" in plugin_name:
            return
        if not os.path.isfile(plugin_path):
            print(f"Plugin file {plugin_path} does not exist.")
            return

        try:
            spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # Print the module's attributes to check its contents
            print(f"Loaded module: {plugin_name}")
            # print(f"Module attributes: {dir(module)}")

            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if isinstance(attr, type) and issubclass(attr, Case) and attr is not Case:
                    instance = attr()  # 创建插件实例
                    case_name = instance.case_name
                    self.plugins[case_name] = instance
                    instance.running = False
                    print(f"Loaded plugin: {plugin_name} (Case Name: {instance.case_name})")
                    return

            print(f"No valid Case class found in {plugin_name}.py")
        except Exception as e:
            print(f"Error loading plugin {plugin_name}: {e}")

    def load_all_plugins(self):
        # 遍历插件目录，加载所有插件
        self.plugins.clear()  # 清空已加载的插件
        for filename in os.listdir(self.plugin_dir):
            if filename.endswith(".py"):
                plugin_name = filename[:-3]  # 去掉 .py 扩展名
                self.load_plugin(plugin_name)

    def unload_plugin(self, case_name):
        if case_name in self.plugins:
            del sys.modules[self.plugins[case_name].__class__.__module__]
            del self.plugins[case_name]
            print(f"Unloaded plugin: {case_name}")
        else:
            print(f"Plugin {case_name} not loaded.")

    def list_plugins(self):
        return list(self.plugins.keys())

    def show_plugin_info(self, case_name):
        if case_name in self.plugins:
            return self.plugins[case_name].show()
        else:
            print(f"Plugin {case_name} not loaded.")
            return None

    def run_plugin(self, case_name):
        try:
            if case_name in self.plugins:
                plugin = self.plugins[case_name]
                if plugin.running:
                    print(f"Plugin {case_name} is already running.")
                    return
                # 创建并启动新的线程来执行插件
                thread = PluginThread(plugin)
                self.plugin_threads[case_name] = thread
                thread.start()

                plugin.running = True  # 标记插件为正在运行
            else:
                print(f"Plugin {case_name} not loaded.")
        except Exception as e:
            print(f"插件启动失败 原因：{e}")
    

    def stop_plugin(self, case_name):
        if case_name in self.plugins:
            plugin = self.plugins[case_name]
            if plugin.running:
                thread = self.plugin_threads.get(case_name)
                if thread:
                    thread.stop()  # Set the stop event to signal the thread to exit
                plugin.running = False  # Update plugin status
                print(f"Plugin {case_name} has been stopped.")
            else:
                print(f"Plugin {case_name} is not running.")
        else:
            print(f"Plugin {case_name} not loaded.")

    def execute_method(self, case_name, method_name, *args, **kwargs):
            """
            执行指定插件实例的任意方法。

            :param case_name: 用例名称 (插件实例的 key)
            :param method_name: 要执行的方法名称
            :param args: 位置参数
            :param kwargs: 关键字参数
            """
            if case_name not in self.plugins:
                print(f"Plugin {case_name} not loaded.")
                return None

            instance = self.plugins[case_name]
            if hasattr(instance, method_name):
                method = getattr(instance, method_name)
                if callable(method):
                    try:
                        return method(*args, **kwargs)
                    except Exception as e:
                        print(f"Error executing method '{method_name}' in plugin '{case_name}': {e}")
                else:
                    print(f"'{method_name}' in plugin '{case_name}' is not callable.")
            else:
                print(f"Method '{method_name}' not found in plugin '{case_name}'.")
