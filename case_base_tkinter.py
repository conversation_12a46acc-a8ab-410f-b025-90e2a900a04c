# case_base_tkinter.py

from abc import ABC, abstractmethod
from paddleocr import PaddleOCR
from ultralytics import YOLO
from PIL import Image
import numpy as np
import supervision as sv
import psutil
import win32gui
import win32ui
import win32con
import win32api
import win32process
import cv2
import time
import threading


class Case(ABC):
    def __init__(self):
        """
        推荐要包含如下的参数
        """
        self.case_name = "Default Case Name"
        self.key_code_map = {
            'A': 0x41, 'B': 0x42, 'C': 0x43, 'D': 0x44, 'E': 0x45, 'F': 0x46, 'G': 0x47,
            'H': 0x48, 'I': 0x49, 'J': 0x4A, 'K': 0x4B, 'L': 0x4C, 'M': 0x4D, 'N': 0x4E,
            'O': 0x4F, 'P': 0x50, 'Q': 0x51, 'R': 0x52, 'S': 0x53, 'T': 0x54, 'U': 0x55,
            'V': 0x56, 'W': 0x57, 'X': 0x58, 'Y': 0x59, 'Z': 0x5A,
            '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35, '6': 0x36,
            '7': 0x37, '8': 0x38, '9': 0x39,
            'ENTER': 0x0D, 'ESC': 0x1B, 'SPACE': 0x20, 'BACKSPACE': 0x08, 'TAB': 0x09,
            'LEFT': 0x25, 'UP': 0x26, 'RIGHT': 0x27, 'DOWN': 0x28,
            'SHIFT': 0x10, 'CTRL': 0x11, 'ALT': 0x12, 'CAPSLOCK': 0x14,
            'F1': 0x70, 'F2': 0x71, 'F3': 0x72, 'F4': 0x73, 'F5': 0x74, 'F6': 0x75,
            'F7': 0x76, 'F8': 0x77, 'F9': 0x78, 'F10': 0x79, 'F11': 0x7A, 'F12': 0x7B,
            "CTRL": 0x11
        }
        self._pid = None
        self._hwnd = None
        self.models = {}
        self.model = None
        self.stop_flag = threading.Event()
        self.lock = threading.Lock()
        self.running = False

    @abstractmethod
    def show(self):
        """展示用例执行信息方法"""
        print("展示用例执行信息方法 应当返回str类型")

    @abstractmethod
    def run(self, stop_flag):
        """
        Args:
            stop_flag: 设置给self.stop_flag
        Returns: 无
        """
        self.stop_flag = stop_flag
        print("执行用例方法")

    def get_window_titles_by_pid(self, target_pid):
        """根据PID获取窗口标题"""
        def enum_windows_callback(hwnd, titles):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    if pid == target_pid:
                        title = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        if title:  # 仅添加非空标题
                            titles.append([title, class_name])
                except Exception as e:
                    print(f"获取窗口信息时出错: {e}")
            return True

        window_titles = []
        try:
            win32gui.EnumWindows(enum_windows_callback, window_titles)
        except Exception as e:
            print(f"枚举窗口时出错: {e}")
        return window_titles

    def find_process_by_name(self, process_name):
        """通过进程名字获取程序的pid"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] == process_name:
                    title = self.get_window_titles_by_pid(proc.info['pid'])
                    if title and (("3" in title[0][0]) or ("剑" in title[0][0])) and "KGWin32App" in title[0][1]:
                        self._pid = proc.info['pid']
                        return proc.info['pid']
        except Exception as e:
            print(f"查找进程时出错: {e}")
        return None

    def get_window_by_pid(self, pid):
        """获取程序的窗口句柄"""
        def enum_windows_callback(hwnd, pid_to_check):
            try:
                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                if window_pid == pid_to_check:
                    window_handles.append(hwnd)
            except Exception as e:
                print(f"获取窗口PID时出错: {e}")

        window_handles = []
        try:
            win32gui.EnumWindows(enum_windows_callback, pid)
            if window_handles:
                self._hwnd = window_handles[0]
                return window_handles[0]
        except Exception as e:
            print(f"枚举窗口时出错: {e}")
        return None

    def get_window_screenshot(self, hwnd):
        """获取程序的截图"""
        try:
            with self.lock:
                # 检查窗口是否有效
                if not win32gui.IsWindow(hwnd):
                    print("无效的窗口句柄")
                    return None
                    
                # 获取客户区的DC
                client_dc = win32gui.GetDC(hwnd)
                if not client_dc:
                    print("无法获取窗口DC")
                    return None

                dc_object = win32ui.CreateDCFromHandle(client_dc)
                compatible_dc = dc_object.CreateCompatibleDC()

                # 获取客户区的位置
                try:
                    client_rect = win32gui.GetClientRect(hwnd)
                    left, top, right, bottom = client_rect
                    width = right - left
                    height = bottom - top
                    
                    if width <= 0 or height <= 0:
                        print(f"无效的窗口尺寸: {width}x{height}")
                        return None
                except Exception as e:
                    print(f"获取客户区矩形时出错: {e}")
                    return None

                # 创建位图对象
                bitmap = win32ui.CreateBitmap()
                bitmap.CreateCompatibleBitmap(dc_object, width, height)
                compatible_dc.SelectObject(bitmap)

                # 抓取客户区内容
                compatible_dc.BitBlt((0, 0), (width, height), dc_object, (left, top), win32con.SRCCOPY)

                # 获取图像数据
                bitmap_bits = bitmap.GetBitmapBits(True)
                image = Image.frombuffer('RGB', (width, height), bitmap_bits, 'raw', 'BGRX', 0, 1)
                np_image = np.array(image)

                # 清理资源
                dc_object.DeleteDC()
                compatible_dc.DeleteDC()
                win32gui.ReleaseDC(hwnd, client_dc)
                win32gui.DeleteObject(bitmap.GetHandle())

                return np_image
                
        except Exception as e:
            print(f"截图时出错: {e}")
            return None

    def screenshot_stream(self, hwnd, stop_flag):
        """截图流生成器"""
        while not stop_flag.is_set():
            try:
                screenshot = self.get_window_screenshot(hwnd)
                if screenshot is not None and screenshot.size > 0:
                    # 转为 OpenCV 格式
                    screenshot_cv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
                    yield screenshot_cv
                else:
                    # 如果截图失败，等待一段时间后重试
                    time.sleep(0.1)
                    continue
            except Exception as e:
                print(f"截图流生成时出错: {e}")
                time.sleep(0.5)  # 出错时等待更长时间

    def pic(self):
        """用于给主程序生成画面"""
        msg = f"{self.case_name}: 展示用例截图"
        print(msg)

        try:
            # 检查必要的属性
            if not self._hwnd:
                print("窗口句柄未设置")
                return

            if not self.model:
                print("模型未设置")
                return

            bounding = sv.BoxAnnotator()
            label = sv.LabelAnnotator()

            stream = self.screenshot_stream(self._hwnd, self.stop_flag)
            for frame in stream:
                if frame is None:
                    continue

                try:
                    results = self.model.predict(source=frame, verbose=False)[0]
                    detections = sv.Detections.from_ultralytics(results)

                    annotated_image = bounding.annotate(scene=frame, detections=detections)
                    annotated_image = label.annotate(scene=annotated_image, detections=detections)

                    # 添加置信度和标签信息
                    for detection in detections:
                        label_name = detection[5]["class_name"]
                        x1, y1, x2, y2 = map(int, detection[0][0:4])
                        confidence = detection[2]

                        text = f'{confidence:.2f}'
                        font = cv2.FONT_HERSHEY_SIMPLEX
                        font_scale = 0.6
                        color = (0, 255, 0)
                        thickness = 2

                        cv2.putText(annotated_image, text, (x1, y2 + 15), font, font_scale, color, thickness)
                        cv2.putText(annotated_image, label_name, (x1 + 50, y2 + 15), font, font_scale, color, thickness)

                    # 直接返回OpenCV格式的图片（BGR）
                    yield annotated_image

                except Exception as e:
                    print(f"处理帧时出错: {e}")
                    continue

        except Exception as e:
            print(f"图片生成器初始化失败: {e}")

    def send_click(self, hwnd, x, y, img=None, debug=0):
        """向对应的程序发送点击事件"""
        try:
            # 检查窗口是否有效
            if not win32gui.IsWindow(hwnd):
                print("无效的窗口句柄")
                return

            lparam = win32api.MAKELONG(int(x), int(y))
            win32gui.SendMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lparam)
            time.sleep(0.1)
            win32gui.SendMessage(hwnd, win32con.WM_LBUTTONUP, None, lparam)

            if debug == 1 and img is not None:
                img_with_marker = img.copy()
                cv2.circle(img_with_marker, (int(x), int(y)), 15, (0, 0, 255), -1)
                cv2.imshow("click", img_with_marker)

        except Exception as e:
            print(f"发送点击事件时出错: {e}")

    def send_key_press(self, hwnd, keys):
        """向指定程序发送按键"""
        try:
            if not win32gui.IsWindow(hwnd):
                print("无效的窗口句柄")
                return
                
            if isinstance(keys, str):
                keys = [keys.upper()]
            else:
                keys = [key.upper() for key in keys]

            # 发送按键按下事件
            for key in keys:
                if key in self.key_code_map:
                    virtual_key = self.key_code_map[key]
                    win32gui.SendMessage(hwnd, win32con.WM_KEYDOWN, virtual_key, 0)
                else:
                    print(f"按键 '{key}' 不在按键映射中")

            # 发送按键释放事件
            for key in reversed(keys):
                if key in self.key_code_map:
                    virtual_key = self.key_code_map[key]
                    win32gui.SendMessage(hwnd, win32con.WM_KEYUP, virtual_key, 0)

        except Exception as e:
            print(f"发送按键事件时出错: {e}")

    def get_ocr(self, img, location, mode=1, debug=0):
        """对指定图像进行OCR识别"""
        try:
            x1, y1, x2, y2 = map(int, location)
            cropped_image = img[int(y1):int(y2), int(x1):int(x2)]

            gray_image = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)

            if mode == 1:
                ret, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_TOZERO)
            elif mode == 2:
                ret, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_TOZERO)

            if debug == 1:
                cv2.imshow("local", gray_image)
                cv2.imshow("gray", thresh_image)

            ocr = PaddleOCR(use_angle_cls=True, lang="ch")
            result = ocr.ocr(thresh_image, cls=True)

            if result[0]:
                return result[0][0][1][0]
            else:
                return None

        except Exception as e:
            print(f"OCR识别时出错: {e}")
            return None
