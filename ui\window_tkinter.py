# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import threading


class MainWindow:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        
    def setup_ui(self):
        # 设置主窗口
        self.root.title("MainWindow")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建水平布局
        self.create_horizontal_layout(main_frame)
        
        # 创建状态栏
        self.create_statusbar()
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入用例", command=self.import_case)
        file_menu.add_command(label="重载用例库", command=self.reload_case_library)
        
        # 操作菜单
        operation_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="操作", menu=operation_menu)
        operation_menu.add_command(label="开始 F9", command=self.run_selected_case, accelerator="F9")
        operation_menu.add_command(label="停止 F10", command=self.stop_case, accelerator="F10")
        
        # 绑定快捷键
        self.root.bind('<F9>', lambda e: self.run_selected_case())
        self.root.bind('<F10>', lambda e: self.stop_case())
        
    def create_horizontal_layout(self, parent):
        # 创建水平分割的PanedWindow
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：用例列表
        self.create_case_list(paned_window)
        
        # 右侧：显示区域
        self.create_display_area(paned_window)
        
    def create_case_list(self, parent):
        # 左侧框架
        left_frame = ttk.Frame(parent)
        parent.add(left_frame, weight=1)
        
        # 用例列表
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 列表标题
        ttk.Label(list_frame, text="用例列表").pack(anchor=tk.W)
        
        # 创建Listbox和滚动条
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.case_listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set)
        self.case_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.case_listbox.yview)
        
    def create_display_area(self, parent):
        # 右侧框架
        right_frame = ttk.Frame(parent)
        parent.add(right_frame, weight=7)
        
        # 创建垂直布局
        self.create_vertical_layout(right_frame)
        
    def create_vertical_layout(self, parent):
        # 上半部分：图片显示区域
        self.create_image_display(parent)
        
        # 下半部分：其他显示区域
        self.create_bottom_display(parent)
        
    def create_image_display(self, parent):
        # 图片显示框架
        image_frame = ttk.LabelFrame(parent, text="图片显示", padding=10)
        image_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图片标签
        self.pic_label = ttk.Label(image_frame, text="TextLabel", anchor=tk.CENTER)
        self.pic_label.pack(fill=tk.BOTH, expand=True)
        
    def create_bottom_display(self, parent):
        # 底部显示框架
        bottom_frame = ttk.LabelFrame(parent, text="信息显示", padding=10)
        bottom_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 可以在这里添加其他控件
        info_label = ttk.Label(bottom_frame, text="信息显示区域")
        info_label.pack(anchor=tk.W)
        
    def create_statusbar(self):
        # 状态栏
        self.statusbar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.statusbar.pack(side=tk.BOTTOM, fill=tk.X)
        
    # 以下是事件处理方法，需要在主程序中实现具体逻辑
    def import_case(self):
        """导入用例的回调方法"""
        pass
        
    def reload_case_library(self):
        """重载用例库的回调方法"""
        pass
        
    def run_selected_case(self):
        """运行选中用例的回调方法"""
        pass
        
    def stop_case(self):
        """停止用例的回调方法"""
        pass
        
    def update_case_list(self, case_names):
        """更新用例列表"""
        self.case_listbox.delete(0, tk.END)
        for name in case_names:
            self.case_listbox.insert(tk.END, name)
            
    def get_selected_case(self):
        """获取选中的用例"""
        selection = self.case_listbox.curselection()
        if selection:
            return self.case_listbox.get(selection[0])
        return None
        
    def set_image(self, image):
        """设置显示的图片"""
        if isinstance(image, Image.Image):
            try:
                # 获取标签的当前尺寸
                labelWidth = self.pic_label.winfo_width()
                labelHeight = self.pic_label.winfo_height()

                # 如果标签尺寸还没有确定，使用默认值
                if labelWidth <= 1 or labelHeight <= 1:
                    labelWidth = 600
                    labelHeight = 400

                # 计算缩放比例，保持宽高比
                imgWidth, imgHeight = image.size
                scaleX = labelWidth / imgWidth
                scaleY = labelHeight / imgHeight
                scale = min(scaleX, scaleY, 1.0)  # 不放大，只缩小

                if scale < 1.0:
                    newWidth = int(imgWidth * scale)
                    newHeight = int(imgHeight * scale)
                    image = image.resize((newWidth, newHeight), Image.Resampling.LANCZOS)

                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(image)
                self.pic_label.configure(image=photo, text="")
                self.pic_label.image = photo  # 保持引用

            except Exception as e:
                print(f"设置图片时出错: {e}")
                self.pic_label.configure(image="", text="图片显示错误")
        else:
            # 清空图片
            self.pic_label.configure(image="", text="TextLabel")
            
    def clear_image(self):
        """清空图片显示"""
        self.pic_label.configure(image="", text="TextLabel")
        
    def set_status(self, text):
        """设置状态栏文本"""
        self.statusbar.configure(text=text)
