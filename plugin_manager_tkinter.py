# plugin_manager_tkinter.py

import importlib.util
import os
import sys
from case_base_tkinter import *
import threading
import traceback


class PluginThread(threading.Thread):
    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin
        self.stopFlag = threading.Event()
        self.running = False
        self.daemon = True  # 设置为守护线程

    def run(self):
        try:
            self.plugin.run(stopFlag=self.stopFlag)  # 启动插件的任务
            self.running = True
        except Exception as e:
            tbStr = traceback.format_exc()
            print(f"插件执行错误: {e}")
            print("详细错误信息:")
            print(tbStr)
            self.running = False

    def stop(self):
        self.stopFlag.set()
        self.running = False


class PluginManager:
    def __init__(self, pluginDir='plugins'):
        super().__init__()
        self.pluginDir = pluginDir
        self.plugins = {}
        self.pluginThreads = {}

    def loadPlugin(self, pluginName):
        """加载单个插件"""
        pluginPath = os.path.join(self.pluginDir, f"{pluginName}.py")
        if "case_base" in pluginName:
            return
        if not os.path.isfile(pluginPath):
            print(f"插件文件 {pluginPath} 不存在")
            return

        try:
            spec = importlib.util.spec_from_file_location(pluginName, pluginPath)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            print(f"已加载模块: {pluginName}")

            for attrName in dir(module):
                attr = getattr(module, attrName)
                if isinstance(attr, type) and issubclass(attr, Case) and attr is not Case:
                    instance = attr()  # 创建插件实例
                    caseName = instance.caseName
                    self.plugins[caseName] = instance
                    instance.running = False
                    print(f"已加载插件: {pluginName} (用例名称: {instance.caseName})")
                    return

            print(f"在 {pluginName}.py 中未找到有效的 Case 类")
        except Exception as e:
            print(f"加载插件 {pluginName} 时出错: {e}")
            traceback.print_exc()

    def loadAllPlugins(self):
        """加载所有插件"""
        self.plugins.clear()  # 清空已加载的插件
        for filename in os.listdir(self.pluginDir):
            if filename.endswith(".py"):
                pluginName = filename[:-3]  # 去掉 .py 扩展名
                self.loadPlugin(pluginName)

    def unloadPlugin(self, caseName):
        """卸载插件"""
        if caseName in self.plugins:
            if caseName in self.pluginThreads:
                self.stopPlugin(caseName)
            del sys.modules[self.plugins[caseName].__class__.__module__]
            del self.plugins[caseName]
            print(f"已卸载插件: {caseName}")
        else:
            print(f"插件 {caseName} 未加载")

    def listPlugins(self):
        """列出所有插件"""
        return list(self.plugins.keys())

    def showPluginInfo(self, caseName):
        """显示插件信息"""
        if caseName in self.plugins:
            return self.plugins[caseName].show()
        else:
            print(f"插件 {caseName} 未加载")
            return None

    def runPlugin(self, caseName):
        """运行插件"""
        try:
            if caseName in self.plugins:
                plugin = self.plugins[caseName]
                if plugin.running:
                    print(f"插件 {caseName} 已在运行中")
                    return
                # 创建并启动新的线程来执行插件
                thread = PluginThread(plugin)
                self.pluginThreads[caseName] = thread
                thread.start()

                plugin.running = True  # 标记插件为正在运行
                print(f"已启动插件: {caseName}")
            else:
                print(f"插件 {caseName} 未加载")
        except Exception as e:
            print(f"插件启动失败，原因：{e}")
            traceback.print_exc()

    def stopPlugin(self, caseName):
        """停止插件"""
        if caseName in self.plugins:
            plugin = self.plugins[caseName]
            if plugin.running:
                thread = self.pluginThreads.get(caseName)
                if thread:
                    thread.stop()  # 设置停止标志
                plugin.running = False  # 更新插件状态
                print(f"插件 {caseName} 已停止")
            else:
                print(f"插件 {caseName} 未在运行")
        else:
            print(f"插件 {caseName} 未加载")

    def executeMethod(self, caseName, methodName, *args, **kwargs):
        """执行指定插件实例的任意方法"""
        if caseName not in self.plugins:
            print(f"插件 {caseName} 未加载")
            return None

        instance = self.plugins[caseName]
        if hasattr(instance, methodName):
            method = getattr(instance, methodName)
            if callable(method):
                try:
                    return method(*args, **kwargs)
                except Exception as e:
                    print(f"执行方法 '{methodName}' 在插件 '{caseName}' 中时出错: {e}")
                    traceback.print_exc()
            else:
                print(f"插件 '{caseName}' 中的 '{methodName}' 不可调用")
        else:
            print(f"插件 '{caseName}' 中未找到方法 '{methodName}'")

    # 兼容性方法（保持与原版本的接口一致）
    def load_plugin(self, pluginName):
        return self.loadPlugin(pluginName)
    
    def load_all_plugins(self):
        return self.loadAllPlugins()
    
    def run_plugin(self, caseName):
        return self.runPlugin(caseName)
    
    def stop_plugin(self, caseName):
        return self.stopPlugin(caseName)
    
    def execute_method(self, caseName, methodName, *args, **kwargs):
        return self.executeMethod(caseName, methodName, *args, **kwargs)
