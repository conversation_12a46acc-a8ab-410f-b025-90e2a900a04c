# plugin_manager_tkinter.py

import importlib.util
import os
import sys
from case_base_tkinter import *
import threading
import traceback


class PluginThread(threading.Thread):
    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin
        self.stop_flag = threading.Event()
        self.running = False
        self.daemon = True  # 设置为守护线程

    def run(self):
        try:
            self.plugin.run(stop_flag=self.stop_flag)  # 启动插件的任务
            self.running = True
        except Exception as e:
            tb_str = traceback.format_exc()
            print(f"插件执行错误: {e}")
            print("详细错误信息:")
            print(tb_str)
            self.running = False

    def stop(self):
        self.stop_flag.set()
        self.running = False


class PluginManager:
    def __init__(self, plugin_dir='plugins'):
        super().__init__()
        self.plugin_dir = plugin_dir
        self.plugins = {}
        self.plugin_threads = {}

    def load_plugin(self, plugin_name):
        """加载单个插件"""
        plugin_path = os.path.join(self.plugin_dir, f"{plugin_name}.py")
        if "case_base" in plugin_name:
            return
        if not os.path.isfile(plugin_path):
            print(f"插件文件 {plugin_path} 不存在")
            return

        try:
            spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            print(f"已加载模块: {plugin_name}")

            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                # 检查是否是有效的Case类
                is_valid_case = False
                if isinstance(attr, type):
                    # 尝试导入并检查是否是Case的子类
                    try:
                        from case_base_tkinter import Case as TkinterCase
                        if issubclass(attr, TkinterCase) and attr is not TkinterCase:
                            is_valid_case = True
                    except ImportError:
                        pass

                    if not is_valid_case:
                        try:
                            from case_base import Case as OriginalCase
                            if issubclass(attr, OriginalCase) and attr is not OriginalCase:
                                is_valid_case = True
                        except ImportError:
                            pass

                if is_valid_case:
                    instance = attr()  # 创建插件实例
                    # 兼容两种命名方式
                    case_name = getattr(instance, 'case_name', None) or getattr(instance, 'caseName', f"未命名_{plugin_name}")
                    self.plugins[case_name] = instance
                    instance.running = False
                    print(f"已加载插件: {plugin_name} (用例名称: {case_name})")
                    return

            print(f"在 {plugin_name}.py 中未找到有效的 Case 类")
        except Exception as e:
            print(f"加载插件 {plugin_name} 时出错: {e}")
            traceback.print_exc()

    def load_all_plugins(self):
        """加载所有插件"""
        self.plugins.clear()  # 清空已加载的插件
        for filename in os.listdir(self.plugin_dir):
            if filename.endswith(".py"):
                plugin_name = filename[:-3]  # 去掉 .py 扩展名
                self.load_plugin(plugin_name)

    def unload_plugin(self, case_name):
        """卸载插件"""
        if case_name in self.plugins:
            if case_name in self.plugin_threads:
                self.stop_plugin(case_name)
            del sys.modules[self.plugins[case_name].__class__.__module__]
            del self.plugins[case_name]
            print(f"已卸载插件: {case_name}")
        else:
            print(f"插件 {case_name} 未加载")

    def list_plugins(self):
        """列出所有插件"""
        return list(self.plugins.keys())

    def show_plugin_info(self, case_name):
        """显示插件信息"""
        if case_name in self.plugins:
            return self.plugins[case_name].show()
        else:
            print(f"插件 {case_name} 未加载")
            return None

    def run_plugin(self, case_name):
        """运行插件"""
        try:
            if case_name in self.plugins:
                plugin = self.plugins[case_name]
                if plugin.running:
                    print(f"插件 {case_name} 已在运行中")
                    return
                # 创建并启动新的线程来执行插件
                thread = PluginThread(plugin)
                self.plugin_threads[case_name] = thread
                thread.start()

                plugin.running = True  # 标记插件为正在运行
                print(f"已启动插件: {case_name}")
            else:
                print(f"插件 {case_name} 未加载")
        except Exception as e:
            print(f"插件启动失败，原因：{e}")
            traceback.print_exc()

    def stop_plugin(self, case_name):
        """停止插件"""
        if case_name in self.plugins:
            plugin = self.plugins[case_name]
            if plugin.running:
                thread = self.plugin_threads.get(case_name)
                if thread:
                    thread.stop()  # 设置停止标志
                plugin.running = False  # 更新插件状态
                print(f"插件 {case_name} 已停止")
            else:
                print(f"插件 {case_name} 未在运行")
        else:
            print(f"插件 {case_name} 未加载")

    def execute_method(self, case_name, method_name, *args, **kwargs):
        """执行指定插件实例的任意方法"""
        if case_name not in self.plugins:
            print(f"插件 {case_name} 未加载")
            return None

        instance = self.plugins[case_name]
        if hasattr(instance, method_name):
            method = getattr(instance, method_name)
            if callable(method):
                try:
                    return method(*args, **kwargs)
                except Exception as e:
                    print(f"执行方法 '{method_name}' 在插件 '{case_name}' 中时出错: {e}")
                    traceback.print_exc()
            else:
                print(f"插件 '{case_name}' 中的 '{method_name}' 不可调用")
        else:
            print(f"插件 '{case_name}' 中未找到方法 '{method_name}'")


