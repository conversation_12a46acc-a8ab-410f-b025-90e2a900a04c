from case_base import *

class Plugin1(Case):
    #固定格式照抄
    def __init__(self):
        super().__init__()
        self.case_name = "绝境战场1"
        self.process_name = "JX3ClientX3DX64.exe"
        self.init_model()

    # 固定格式照抄
    def init_process(self,model,stop_flag = None):
        self.find_process_by_name( self.process_name)
        self.get_window_by_pid(self._pid)
        self.get_window_screenshot(self._hwnd)
        if stop_flag:
            self.stop_flag = stop_flag
        self.model = self.models[model]

    # 固定格式 根据自己的模型改变内容
    def init_model(self):
        self.models["chiji"] = YOLO("models/chiji.pt")

    def show(self):
        msg = f"{self.case_name}: 展示用例执行信息"
        print(msg)
        return msg

    def process(self):
        stream = self.screenshot_stream(self._hwnd, self.stop_flag)
        self.send_key_press(self._hwnd, ["ctrl", "l"])
        for frame in stream:
            if frame is None:
                continue
            results = self.model.predict(source=frame)[0]

            if len(results.boxes.xyxy) > 0:
                class_name = self.model.names
                class_id = results.boxes.cls.cpu().numpy()
                confidence_scores = results.boxes.conf.cpu().numpy()
                loactions = results.boxes.xyxy.cpu().numpy()

                names = {}
                for id, conf, location in zip(class_id, confidence_scores, loactions):
                    data = [conf, location]
                    names[class_name[id]] = data

                for id, conf, location in zip(class_id, confidence_scores, loactions):
                    name = class_name[id]
                    if name == "ocr_huangse_anniu":
                        res = self.get_ocr(frame,location,debug=0)
                        if res:
                            x1, y1, x2, y2 = map(int, location)
                            x = x1 + (x2 - x1)/2
                            y = y1 + (y2 - y1)/2
                            self.send_click(self._hwnd,x,y)
                            break
            else:
                self.send_key_press(self._hwnd, ["ctrl", "l"])

            # 运行太快了 保证画面出现
            time.sleep(10)

    # 固定格式照抄
    def run(self,stop_flag):
        self.init_process("chiji",stop_flag)
        self.process()

