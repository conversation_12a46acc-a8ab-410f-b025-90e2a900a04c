<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,7">
      <property name="sizeConstraint">
       <enum>QLayout::SizeConstraint::SetMinAndMaxSize</enum>
      </property>
      <item>
       <widget class="QListView" name="case_listView"/>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_3" stretch="5,5">
        <property name="spacing">
         <number>5</number>
        </property>
        <property name="sizeConstraint">
         <enum>QLayout::SizeConstraint::SetMinAndMaxSize</enum>
        </property>
        <item>
         <widget class="QWidget" name="widget_6" native="true">
          <property name="styleSheet">
           <string notr="true">border: 2px solid black;    
border-radius: 5px;        
</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_7" stretch="0">
             <property name="spacing">
              <number>6</number>
             </property>
             <property name="sizeConstraint">
              <enum>QLayout::SizeConstraint::SetMinAndMaxSize</enum>
             </property>
             <item>
              <widget class="QLabel" name="pic_label">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="autoFillBackground">
                <bool>false</bool>
               </property>
               <property name="text">
                <string>TextLabel</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_7" native="true">
          <property name="styleSheet">
           <string notr="true">border: 2px solid black;     /* 黑色2像素宽的实线边框 */
border-radius: 5px;          /* 5像素圆角 */
</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="add_case"/>
    <addaction name="reload_caselib"/>
   </widget>
   <widget class="QMenu" name="menu_2">
    <property name="title">
     <string>操作</string>
    </property>
    <addaction name="run"/>
    <addaction name="stop"/>
   </widget>
   <addaction name="menu"/>
   <addaction name="menu_2"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="add_case">
   <property name="text">
    <string>导入用例</string>
   </property>
  </action>
  <action name="run">
   <property name="text">
    <string>开始 F9</string>
   </property>
   <property name="shortcut">
    <string>F9</string>
   </property>
  </action>
  <action name="stop">
   <property name="text">
    <string>停止 F10</string>
   </property>
   <property name="shortcut">
    <string>F10</string>
   </property>
  </action>
  <action name="reload_caselib">
   <property name="text">
    <string>重载用例库</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
