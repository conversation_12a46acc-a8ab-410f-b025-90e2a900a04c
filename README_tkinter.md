# 从PyQt5迁移到tkinter

## 概述

本项目已从PyQt5迁移到tkinter，以减少外部依赖并使用Python标准库。

## 主要变化

### 1. 新文件
- `ui/window_tkinter.py` - 新的tkinter UI实现
- `ui_tkinter.py` - 新的主程序文件
- `test_tkinter_ui.py` - UI测试脚本

### 2. 依赖变化
- **移除**: PyQt5, PyQt5_sip
- **保留**: Pillow (用于图片处理)
- **新增**: 无 (tkinter是Python标准库)

### 3. 功能对比

| 功能 | PyQt5版本 | tkinter版本 |
|------|-----------|-------------|
| 主窗口 | QMainWindow | tk.Tk + ttk.Frame |
| 菜单栏 | QMenuBar | tk.Menu |
| 用例列表 | QListView | tk.Listbox |
| 图片显示 | QLabel + QPixmap | ttk.Label + ImageTk.PhotoImage |
| 布局管理 | QGridLayout, QHBoxLayout | ttk.PanedWindow, pack |
| 状态栏 | QStatusBar | ttk.Label |
| 定时器 | QTimer | root.after() |

## 使用方法

### 运行新版本UI
```bash
python ui_tkinter.py
```

### 测试UI功能
```bash
python test_tkinter_ui.py
```

### 运行原版本UI (如果需要)
```bash
python ui.py
```

## 迁移说明

### 图片处理
- PyQt5使用QImage/QPixmap
- tkinter使用PIL Image + ImageTk.PhotoImage
- 需要在`qimage_to_pil()`方法中处理图片格式转换

### 定时器
- PyQt5: `QTimer.timeout.connect()`
- tkinter: `root.after(delay, callback)`

### 事件处理
- PyQt5: 信号槽机制
- tkinter: 直接方法绑定

## 优势

1. **无外部依赖**: tkinter是Python标准库
2. **更轻量**: 不需要安装额外的GUI库
3. **跨平台**: tkinter在所有Python安装中都可用
4. **简单部署**: 减少了依赖管理的复杂性

## 注意事项

1. **图片格式**: 确保图片数据正确转换为PIL格式
2. **性能**: tkinter在高频更新时性能可能不如PyQt5
3. **外观**: tkinter的默认外观较为简单，但可以通过ttk改善

## 兼容性

- Python 3.6+
- 所有主流操作系统 (Windows, macOS, Linux)
- 无需额外安装GUI库

## 下一步

1. 测试所有功能是否正常工作
2. 根据需要调整UI布局和外观
3. 优化图片显示性能
4. 添加更多UI增强功能
