import sys
import tkinter as tk
from tkinter import filedialog, messagebox
from ui.window_tkinter import MainWindow
from plugin_manager import PluginManager
from PIL import Image, ImageTk
import cv2
import numpy as np


class MainApp:
    def __init__(self):
        self.root = tk.Tk()
        self.window = MainWindow(self.root)
        
        # 初始化插件管理器
        self.plugin_manager = PluginManager()
        
        # 连接事件处理方法
        self.setup_event_handlers()
        
        # 加载插件库并显示
        self.reload_case_library()
        
        # 初始化定时器相关变量
        self.timer_id = None
        self.image_generator = None
        
    def setup_event_handlers(self):
        """设置事件处理方法"""
        self.window.import_case = self.import_case
        self.window.reload_case_library = self.reload_case_library
        self.window.run_selected_case = self.run_selected_case
        self.window.stop_case = self.stop_case
        
    def import_case(self):
        """导入用例"""
        file_path = filedialog.askopenfilename(
            title="选择用例文件",
            filetypes=[("Python Files", "*.py"), ("All Files", "*.*")]
        )
        if file_path:
            plugin_name = file_path.split("/")[-1].replace(".py", "")
            try:
                self.plugin_manager.load_plugin(plugin_name)
                self.update_case_list_view()
                self.window.set_status(f"已导入用例: {plugin_name}")
            except Exception as e:
                messagebox.showerror("错误", f"导入用例失败: {str(e)}")
                
    def reload_case_library(self):
        """重新加载插件目录中的所有插件"""
        try:
            self.plugin_manager.load_all_plugins()
            self.update_case_list_view()
            self.window.set_status("用例库已重载")
        except Exception as e:
            messagebox.showerror("错误", f"重载用例库失败: {str(e)}")
            
    def update_case_list_view(self):
        """更新用例列表显示"""
        case_names = list(self.plugin_manager.plugins.keys())
        self.window.update_case_list(case_names)
        
    def run_selected_case(self):
        """运行选中的用例"""
        case_name = self.window.get_selected_case()
        if case_name:
            try:
                self.plugin_manager.run_plugin(case_name)
                self.start_display(case_name)
                self.window.set_status(f"正在运行用例: {case_name}")
            except Exception as e:
                messagebox.showerror("错误", f"运行用例失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")
            
    def stop_case(self):
        """停止用例"""
        case_name = self.window.get_selected_case()
        if case_name:
            try:
                self.stop_display()
                self.plugin_manager.stop_plugin(case_name)
                self.window.set_status(f"已停止用例: {case_name}")
            except Exception as e:
                messagebox.showerror("错误", f"停止用例失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")
            
    def start_display(self, case_name):
        """开始显示图片"""
        # 获取生成器
        self.image_generator = self.plugin_manager.execute_method(case_name, "pic")
        
        if self.image_generator:
            # 开始定时更新帧
            self.update_frame()
            
    def stop_display(self):
        """停止显示"""
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.window.clear_image()
        self.image_generator = None
        
    def update_frame(self):
        """更新帧显示"""
        try:
            if self.image_generator:
                q_image = next(self.image_generator)
                
                # 将QImage转换为PIL Image
                pil_image = self.qimage_to_pil(q_image)
                
                if pil_image:
                    self.window.set_image(pil_image)
                    
                # 设置下一次更新
                self.timer_id = self.root.after(100, self.update_frame)  # 100ms后再次调用
                
        except StopIteration:
            # 生成器结束
            self.stop_display()
        except Exception as e:
            print(f"Error updating frame: {e}")
            self.stop_display()
            
    def qimage_to_pil(self, qimage):
        """将QImage转换为PIL Image"""
        try:
            # 这里需要根据你的QImage格式进行转换
            # 如果qimage实际上是numpy数组或其他格式，需要相应调整
            if hasattr(qimage, 'bits'):
                # 如果是真正的QImage
                width = qimage.width()
                height = qimage.height()
                ptr = qimage.bits()
                ptr.setsize(qimage.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                # 转换为RGB
                rgb_array = cv2.cvtColor(arr, cv2.COLOR_RGBA2RGB)
                return Image.fromarray(rgb_array)
            else:
                # 如果是numpy数组或其他格式
                if isinstance(qimage, np.ndarray):
                    # 确保是RGB格式
                    if len(qimage.shape) == 3 and qimage.shape[2] == 3:
                        return Image.fromarray(qimage)
                    elif len(qimage.shape) == 3 and qimage.shape[2] == 4:
                        # RGBA转RGB
                        rgb_array = cv2.cvtColor(qimage, cv2.COLOR_RGBA2RGB)
                        return Image.fromarray(rgb_array)
                return None
        except Exception as e:
            print(f"Error converting image: {e}")
            return None
            
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    app = MainApp()
    app.run()


if __name__ == "__main__":
    main()
