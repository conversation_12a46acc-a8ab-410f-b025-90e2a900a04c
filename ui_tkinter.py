import sys
import tkinter as tk
from tkinter import filedialog, messagebox
from ui.window_tkinter import MainWindow
from plugin_manager_tkinter import PluginManager
from PIL import Image, ImageTk
import cv2
import numpy as np
import threading
import time


class MainApp:
    def __init__(self):
        self.root = tk.Tk()
        self.window = MainWindow(self.root)

        # 初始化插件管理器
        self.plugin_manager = PluginManager()

        # 连接事件处理方法
        self.setupEventHandlers()

        # 加载插件库并显示
        self.reloadCaseLibrary()

        # 初始化定时器相关变量
        self.timerId = None
        self.imageGenerator = None
        self.isRunning = False

    def setupEventHandlers(self):
        """设置事件处理方法"""
        self.window.import_case = self.importCase
        self.window.reload_case_library = self.reloadCaseLibrary
        self.window.run_selected_case = self.runSelectedCase
        self.window.stop_case = self.stopCase
        
    def importCase(self):
        """导入用例"""
        filePath = filedialog.askopenfilename(
            title="选择用例文件",
            filetypes=[("Python Files", "*.py"), ("All Files", "*.*")]
        )
        if filePath:
            pluginName = filePath.split("/")[-1].replace(".py", "")
            try:
                self.plugin_manager.load_plugin(pluginName)
                self.updateCaseListView()
                self.window.set_status(f"已导入用例: {pluginName}")
            except Exception as e:
                messagebox.showerror("错误", f"导入用例失败: {str(e)}")

    def reloadCaseLibrary(self):
        """重新加载插件目录中的所有插件"""
        try:
            self.plugin_manager.load_all_plugins()
            self.updateCaseListView()
            self.window.set_status("用例库已重载")
        except Exception as e:
            messagebox.showerror("错误", f"重载用例库失败: {str(e)}")

    def updateCaseListView(self):
        """更新用例列表显示"""
        caseNames = list(self.plugin_manager.plugins.keys())
        self.window.update_case_list(caseNames)
        
    def runSelectedCase(self):
        """运行选中的用例"""
        caseName = self.window.get_selected_case()
        if caseName:
            if self.isRunning:
                messagebox.showwarning("警告", "已有用例在运行中，请先停止")
                return
            try:
                self.plugin_manager.run_plugin(caseName)
                self.startDisplay(caseName)
                self.window.set_status(f"正在运行用例: {caseName}")
                self.isRunning = True
            except Exception as e:
                messagebox.showerror("错误", f"运行用例失败: {str(e)}")
                print(f"详细错误信息: {e}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")

    def stopCase(self):
        """停止用例"""
        caseName = self.window.get_selected_case()
        if caseName:
            try:
                self.stopDisplay()
                self.plugin_manager.stop_plugin(caseName)
                self.window.set_status(f"已停止用例: {caseName}")
                self.isRunning = False
            except Exception as e:
                messagebox.showerror("错误", f"停止用例失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")
            
    def startDisplay(self, caseName):
        """开始显示图片"""
        try:
            # 获取生成器
            self.imageGenerator = self.plugin_manager.execute_method(caseName, "pic")

            if self.imageGenerator:
                # 开始定时更新帧
                self.updateFrame()
            else:
                print(f"无法获取用例 {caseName} 的图片生成器")
        except Exception as e:
            print(f"启动显示失败: {e}")
            messagebox.showerror("错误", f"启动显示失败: {str(e)}")

    def stopDisplay(self):
        """停止显示"""
        if self.timerId:
            self.root.after_cancel(self.timerId)
            self.timerId = None
        self.window.clear_image()
        self.imageGenerator = None

    def updateFrame(self):
        """更新帧显示"""
        try:
            if self.imageGenerator and self.isRunning:
                # 获取下一帧
                frameData = next(self.imageGenerator)

                # 转换图片格式
                pilImage = self.convertFrameToPil(frameData)

                if pilImage:
                    self.window.set_image(pilImage)

                # 设置下一次更新
                self.timerId = self.root.after(100, self.updateFrame)  # 100ms后再次调用

        except StopIteration:
            # 生成器结束
            print("图片生成器已结束")
            self.stopDisplay()
        except Exception as e:
            print(f"更新帧时出错: {e}")
            # 继续尝试更新，不立即停止
            if self.isRunning:
                self.timerId = self.root.after(500, self.updateFrame)  # 延长间隔重试
            
    def convertFrameToPil(self, frameData):
        """将帧数据转换为PIL Image"""
        try:
            # 处理不同类型的图片数据
            if frameData is None:
                return None

            # 如果是QImage对象（来自PyQt5）
            if hasattr(frameData, 'bits') and hasattr(frameData, 'width'):
                width = frameData.width()
                height = frameData.height()
                ptr = frameData.bits()
                ptr.setsize(frameData.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                # 转换为RGB
                rgbArray = cv2.cvtColor(arr, cv2.COLOR_RGBA2RGB)
                return Image.fromarray(rgbArray)

            # 如果是numpy数组（OpenCV格式）
            elif isinstance(frameData, np.ndarray):
                if len(frameData.shape) == 3:
                    if frameData.shape[2] == 3:
                        # BGR转RGB（OpenCV默认是BGR）
                        rgbArray = cv2.cvtColor(frameData, cv2.COLOR_BGR2RGB)
                        return Image.fromarray(rgbArray)
                    elif frameData.shape[2] == 4:
                        # BGRA转RGB
                        rgbArray = cv2.cvtColor(frameData, cv2.COLOR_BGRA2RGB)
                        return Image.fromarray(rgbArray)
                elif len(frameData.shape) == 2:
                    # 灰度图
                    return Image.fromarray(frameData, mode='L')

            # 如果已经是PIL Image
            elif isinstance(frameData, Image.Image):
                return frameData

            print(f"不支持的图片数据类型: {type(frameData)}")
            return None

        except Exception as e:
            print(f"转换图片格式时出错: {e}")
            return None
            
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    app = MainApp()
    app.run()


if __name__ == "__main__":
    main()
