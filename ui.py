import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog
from PyQt5.QtCore import QStringListModel,QTimer
from PyQt5.QtGui import QPixmap
from ui.window import Ui_MainWindow  # 假设 window.py 在 ui 子目录中
from plugin_manager import PluginManager  # 插件管理器类

class MainWindow(QMainWindow, Ui_MainWindow):
    def __init__(self):
        super(MainWindow, self).__init__()
        self.setupUi(self)
        
        # 初始化插件管理器
        self.plugin_manager = PluginManager()

        # 连接动作到槽函数
        self.add_case.triggered.connect(self.import_case)          # 导入用例
        self.reload_caselib.triggered.connect(self.reload_case_library)  # 重载用例库
        self.run.triggered.connect(self.run_selected_case)         # 运行选中的用例
        self.stop.triggered.connect(self.stop_case)                # 停止用例

        # 加载插件库并显示
        self.reload_case_library()

    def import_case(self):
        # 弹出文件选择对话框，选择一个插件文件进行加载
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getOpenFileName(self, "选择用例文件", "", "Python Files (*.py)", options=options)
        if file_path:
            plugin_name = file_path.split("/")[-1].replace(".py", "")
            self.plugin_manager.load_plugin(plugin_name)  # 加载单个插件
            self.update_case_list_view()  # 更新界面上的用例列表

    def reload_case_library(self):
        # 重新加载插件目录中的所有插件
        self.plugin_manager.load_all_plugins()  # 加载所有插件
        self.update_case_list_view()  # 更新界面上的用例列表

    def update_case_list_view(self):
        # 更新 QListView，显示所有加载的插件名称
        case_names = list(self.plugin_manager.plugins.keys())

        model = QStringListModel()
        model.setStringList(case_names)
        self.case_listView.setModel(model)

    def run_selected_case(self):
        # 从 QListView 中获取选中的用例并运行
        selected_indexes = self.case_listView.selectedIndexes()
        if selected_indexes:
            case_name = selected_indexes[0].data()
            self.plugin_manager.run_plugin(case_name)  # 使用 case_name 运行插件
            self.start_display(case_name)  # 使用 case_name 运行插件

    def stop_display(self):
        if hasattr(self, 'timer') and self.timer.isActive():
            self.timer.stop()
            self.pic_label.clear()  # 清空显示的图片
        print("Stopped displaying the case.")

    def stop_case(self):
        selected_indexes = self.case_listView.selectedIndexes()
        if selected_indexes:
            case_name = selected_indexes[0].data()
            self.stop_display()  # 停止显示
            self.plugin_manager.stop_plugin(case_name)  # 停止插件的运行

    def start_display(self, case_name):
        # 获取生成器
        self.image_generator = self.plugin_manager.execute_method(case_name, "pic")

        if self.image_generator :
            # 设置 QTimer，每隔一段时间获取下一帧并显示
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_frame)
            self.timer.start(100)  # 每100毫秒刷新一帧，视需求调整间隔co
    def update_frame(self):
        try:
            q_image = next(self.image_generator)
            self.pic_label.setPixmap(QPixmap.fromImage(q_image))
        except StopIteration:
            self.timer.stop()
        except Exception as e:
            print(f"Error updating frame: {e}")
            self.timer.stop()

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()