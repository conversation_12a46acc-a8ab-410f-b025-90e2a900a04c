#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的tkinter UI
"""

import sys
import tkinter as tk
from tkinter import messagebox
from ui.window_tkinter import MainWindow
from plugin_manager_tkinter import PluginManager


def test_plugin_loading():
    """测试插件加载功能"""
    print("=== 测试插件加载功能 ===")
    
    # 创建插件管理器
    plugin_manager = PluginManager()
    
    # 加载所有插件
    plugin_manager.load_all_plugins()
    
    # 显示加载的插件
    plugins = plugin_manager.list_plugins()
    print(f"已加载的插件: {plugins}")
    
    return plugin_manager, plugins


def test_ui_with_plugins():
    """测试UI与插件的集成"""
    print("=== 测试UI与插件集成 ===")
    
    root = tk.Tk()
    window = MainWindow(root)
    
    # 创建插件管理器
    plugin_manager, plugins = test_plugin_loading()
    
    # 更新UI中的插件列表
    window.update_case_list(plugins)
    
    # 设置状态
    window.set_status(f"已加载 {len(plugins)} 个插件")
    
    # 测试事件处理
    def test_import_case():
        messagebox.showinfo("测试", "导入用例功能测试")
        
    def test_reload_case_library():
        plugin_manager.load_all_plugins()
        plugins = plugin_manager.list_plugins()
        window.update_case_list(plugins)
        window.set_status(f"重载完成，共 {len(plugins)} 个插件")
        
    def test_run_selected_case():
        selected = window.get_selected_case()
        if selected:
            messagebox.showinfo("测试", f"运行用例: {selected}")
            # 这里可以测试图片生成功能
            try:
                generator = plugin_manager.execute_method(selected, "pic")
                if generator:
                    window.set_status(f"正在运行: {selected}")
                else:
                    window.set_status(f"无法获取 {selected} 的图片生成器")
            except Exception as e:
                messagebox.showerror("错误", f"运行用例失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")
            
    def test_stop_case():
        selected = window.get_selected_case()
        if selected:
            plugin_manager.stop_plugin(selected)
            window.set_status(f"已停止: {selected}")
        else:
            messagebox.showwarning("警告", "请先选择一个用例")
    
    # 绑定测试方法
    window.import_case = test_import_case
    window.reload_case_library = test_reload_case_library
    window.run_selected_case = test_run_selected_case
    window.stop_case = test_stop_case
    
    print("UI测试启动")
    print("功能说明：")
    print("- 左侧列表显示已加载的插件")
    print("- 可以测试菜单功能")
    print("- 支持F9/F10快捷键")
    print("- 底部显示状态信息")
    
    root.mainloop()


def main():
    """主函数"""
    print("开始测试修复后的tkinter UI")
    
    try:
        # 首先测试插件加载
        plugin_manager, plugins = test_plugin_loading()
        
        if not plugins:
            print("警告: 没有加载到任何插件")
            print("请确保plugins目录中有有效的插件文件")
        
        # 然后测试UI
        test_ui_with_plugins()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
