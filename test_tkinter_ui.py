#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试tkinter UI的简单脚本
"""

import tkinter as tk
from ui.window_tkinter import MainWindow
from PIL import Image, ImageDraw


def test_ui():
    """测试UI基本功能"""
    root = tk.Tk()
    window = MainWindow(root)
    
    # 添加一些测试数据
    test_cases = ["测试用例1", "测试用例2", "测试用例3"]
    window.update_case_list(test_cases)
    
    # 创建一个测试图片
    def create_test_image():
        img = Image.new('RGB', (300, 200), color='lightblue')
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "测试图片", fill='black')
        draw.rectangle([10, 10, 290, 190], outline='red', width=2)
        return img
    
    # 设置测试图片
    test_image = create_test_image()
    window.set_image(test_image)
    
    # 设置状态
    window.set_status("UI测试中...")
    
    # 重写事件处理方法进行测试
    def test_import_case():
        print("测试：导入用例")
        window.set_status("测试：导入用例")
        
    def test_reload_case_library():
        print("测试：重载用例库")
        window.set_status("测试：重载用例库")
        
    def test_run_selected_case():
        selected = window.get_selected_case()
        print(f"测试：运行选中用例 - {selected}")
        window.set_status(f"测试：运行选中用例 - {selected}")
        
    def test_stop_case():
        selected = window.get_selected_case()
        print(f"测试：停止用例 - {selected}")
        window.set_status(f"测试：停止用例 - {selected}")
    
    # 绑定测试方法
    window.import_case = test_import_case
    window.reload_case_library = test_reload_case_library
    window.run_selected_case = test_run_selected_case
    window.stop_case = test_stop_case
    
    print("tkinter UI测试启动")
    print("功能说明：")
    print("- 左侧列表显示测试用例")
    print("- 右上方显示测试图片")
    print("- 菜单栏包含文件和操作菜单")
    print("- 支持F9/F10快捷键")
    print("- 底部显示状态栏")
    
    root.mainloop()


if __name__ == "__main__":
    test_ui()
