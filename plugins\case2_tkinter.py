from case_base_tkinter import *

class Plugin1(Case):
    # 固定格式照抄
    def __init__(self):
        super().__init__()
        self.caseName = "绝境战场_tkinter"  # 使用驼峰命名
        self.processName = "JX3ClientX3DX64.exe"
        self.initModel()

    # 固定格式照抄
    def initProcess(self, model, stopFlag=None):
        """初始化进程"""
        try:
            self.findProcessByName(self.processName)
            if not self._pid:
                print(f"未找到进程: {self.processName}")
                return False
                
            self.getWindowByPid(self._pid)
            if not self._hwnd:
                print(f"未找到窗口句柄")
                return False
                
            # 测试截图功能
            testScreenshot = self.getWindowScreenshot(self._hwnd)
            if testScreenshot is None:
                print("截图测试失败")
                return False
                
            if stopFlag:
                self.stopFlag = stopFlag
            self.model = self.models[model]
            print(f"进程初始化成功: PID={self._pid}, HWND={self._hwnd}")
            return True
        except Exception as e:
            print(f"初始化进程时出错: {e}")
            return False

    # 固定格式 根据自己的模型改变内容
    def initModel(self):
        """初始化模型"""
        try:
            self.models["chiji"] = YOLO("models/chiji.pt")
            print("模型初始化成功")
        except Exception as e:
            print(f"模型初始化失败: {e}")

    def show(self):
        """展示用例信息"""
        msg = f"{self.caseName}: 展示用例执行信息"
        print(msg)
        return msg

    def process(self):
        """处理逻辑"""
        try:
            stream = self.screenshotStream(self._hwnd, self.stopFlag)
            for frame in stream:
                if frame is None:
                    continue
                    
                if self.stopFlag.is_set():
                    print("收到停止信号，退出处理循环")
                    break
                    
                try:
                    results = self.model.predict(source=frame, verbose=False)[0]

                    if len(results.boxes.xyxy) > 0:
                        className = self.model.names
                        classId = results.boxes.cls.cpu().numpy()
                        confidenceScores = results.boxes.conf.cpu().numpy()
                        locations = results.boxes.xyxy.cpu().numpy()

                        names = {}
                        for id, conf, location in zip(classId, confidenceScores, locations):
                            data = [conf, location]
                            names[className[id]] = data

                        for id, conf, location in zip(classId, confidenceScores, locations):
                            name = className[id]
                            if name == "ocr_huangse_anniu":
                                res = self.getOcr(frame, location, debug=0)
                                if res and ("匹" in res or "配" in res or "个" in res or "人" in res or "下一步" in res or "离开" in res or "下" in res):
                                    x1, y1, x2, y2 = map(int, location)
                                    x = x1 + (x2 - x1) / 2
                                    y = y1 + (y2 - y1) / 2
                                    self.sendClick(self._hwnd, x, y)
                                    break
                    else:
                        self.sendKeyPress(self._hwnd, ["ctrl", "l"])

                    # 运行太快了 保证画面出现
                    time.sleep(10)
                    
                except Exception as e:
                    print(f"处理帧时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"处理过程中出错: {e}")

    # 固定格式照抄
    def run(self, stopFlag):
        """运行用例"""
        try:
            if not self.initProcess("chiji", stopFlag):
                print("进程初始化失败，无法运行用例")
                return
            self.process()
        except Exception as e:
            print(f"运行用例时出错: {e}")
        finally:
            print(f"用例 {self.caseName} 运行结束")
