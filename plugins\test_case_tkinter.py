from case_base_tkinter import *
import random

class TestCase(Case):
    """测试用例，用于验证tkinter UI功能"""
    
    def __init__(self):
        super().__init__()
        self.caseName = "测试用例_tkinter"
        self.frameCount = 0
        
    def show(self):
        """展示用例信息"""
        msg = f"{self.caseName}: 这是一个测试用例，用于验证tkinter UI的图片显示功能"
        print(msg)
        return msg
    
    def generateTestFrame(self):
        """生成测试帧"""
        # 创建一个测试图片
        height, width = 400, 600
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 随机背景色
        bgColor = (
            random.randint(50, 100),
            random.randint(50, 100), 
            random.randint(50, 100)
        )
        frame[:] = bgColor
        
        # 添加一些随机矩形
        for i in range(5):
            x1 = random.randint(0, width - 100)
            y1 = random.randint(0, height - 100)
            x2 = x1 + random.randint(50, 100)
            y2 = y1 + random.randint(50, 100)
            
            color = (
                random.randint(100, 255),
                random.randint(100, 255),
                random.randint(100, 255)
            )
            
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, -1)
        
        # 添加文本
        text = f"Frame: {self.frameCount}"
        font = cv2.FONT_HERSHEY_SIMPLEX
        fontScale = 1
        color = (255, 255, 255)
        thickness = 2
        
        # 获取文本尺寸
        textSize = cv2.getTextSize(text, font, fontScale, thickness)[0]
        textX = (width - textSize[0]) // 2
        textY = (height + textSize[1]) // 2
        
        cv2.putText(frame, text, (textX, textY), font, fontScale, color, thickness)
        
        # 添加时间戳
        timeText = f"Time: {time.strftime('%H:%M:%S')}"
        cv2.putText(frame, timeText, (10, 30), font, 0.7, (255, 255, 0), 2)
        
        self.frameCount += 1
        return frame
    
    def pic(self):
        """生成测试图片流"""
        msg = f"{self.caseName}: 开始生成测试图片流"
        print(msg)
        
        try:
            while not self.stopFlag.is_set():
                # 生成测试帧
                frame = self.generateTestFrame()
                
                # 模拟一些检测框
                detections = []
                for i in range(random.randint(1, 3)):
                    x1 = random.randint(50, 400)
                    y1 = random.randint(50, 300)
                    x2 = x1 + random.randint(50, 100)
                    y2 = y1 + random.randint(30, 80)
                    
                    # 绘制检测框
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 添加标签
                    label = f"Object_{i+1}"
                    confidence = random.uniform(0.5, 0.99)
                    labelText = f"{label}: {confidence:.2f}"
                    
                    cv2.putText(frame, labelText, (x1, y1-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                yield frame
                
                # 控制帧率
                time.sleep(0.1)  # 10 FPS
                
        except Exception as e:
            print(f"生成测试图片时出错: {e}")
    
    def run(self, stopFlag):
        """运行测试用例"""
        try:
            self.stopFlag = stopFlag
            print(f"开始运行测试用例: {self.caseName}")
            
            # 模拟一些处理
            while not self.stopFlag.is_set():
                print(f"测试用例运行中... 帧数: {self.frameCount}")
                time.sleep(5)  # 每5秒打印一次状态
                
        except Exception as e:
            print(f"运行测试用例时出错: {e}")
        finally:
            print(f"测试用例 {self.caseName} 运行结束")
