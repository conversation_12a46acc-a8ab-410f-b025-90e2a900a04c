# case_base.py

from abc import ABC, abstractmethod
from paddleocr import PaddleOCR
from PyQt5.QtGui import QImage
from ultralytics import YOLO
from PIL import Image
import numpy as np
import supervision as sv
import psutil
import win32gui
import win32ui
import win32con
import win32api
import win32process
import cv2
import time
import threading

class Case(ABC):
    def __init__(self):
        """
            推荐要包含如下的参数
        """
        self.case_name = "Default Case Name"
        self.key_code_map = \
            {
    'A': 0x41, 'B': 0x42, 'C': 0x43, 'D': 0x44, 'E': 0x45, 'F': 0x46, 'G': 0x47,
    'H': 0x48, 'I': 0x49, 'J': 0x4A, 'K': 0x4B, 'L': 0x4C, 'M': 0x4D, 'N': 0x4E,
    'O': 0x4F, 'P': 0x50, 'Q': 0x51, 'R': 0x52, 'S': 0x53, 'T': 0x54, 'U': 0x55,
    'V': 0x56, 'W': 0x57, 'X': 0x58, 'Y': 0x59, 'Z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35, '6': 0x36,
    '7': 0x37, '8': 0x38, '9': 0x39,
    'ENTER': 0x0D, 'ESC': 0x1B, 'SPACE': 0x20, 'BACKSPACE': 0x08, 'TAB': 0x09,
    'LEFT': 0x25, 'UP': 0x26, 'RIGHT': 0x27, 'DOWN': 0x28,
    'SHIFT': 0x10, 'CTRL': 0x11, 'ALT': 0x12, 'CAPSLOCK': 0x14,
    'F1': 0x70, 'F2': 0x71, 'F3': 0x72, 'F4': 0x73, 'F5': 0x74, 'F6': 0x75,
    'F7': 0x76, 'F8': 0x77, 'F9': 0x78, 'F10': 0x79, 'F11': 0x7A, 'F12': 0x7B,
    "CTRL": 0x11
}
        self._pid = None
        self._hwnd = None
        self.models = {}
        self.model = None
        self.stop_flag = threading.Event()
        self.lock = threading.Lock()

    @abstractmethod
    def show(self):
        """展示用例执行信息方法"""
        print("展示用例执行信息方法 应当返回str类型")

    @abstractmethod
    def run(self,stop_flag):
        """

        Args:
            stop_flag:设置给self.stop_flag

        Returns:无

        """
        self.stop_flag = stop_flag
        print("执行用例方法")

    def get_window_titles_by_pid(self,target_pid):
        def enum_windows_callback(hwnd, titles):
            if win32gui.IsWindowVisible(hwnd):
                # 使用 win32process 获取窗口所属的进程 ID
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                if pid == target_pid:
                    # 获取窗口的标题
                    title = win32gui.GetWindowText(hwnd)
                    classname = win32gui.GetClassName(hwnd)
                    if title:  # 仅添加非空标题
                        titles.append([title,classname])
            return True

        window_titles = []
        win32gui.EnumWindows(enum_windows_callback, window_titles)
        return window_titles

    def find_process_by_name(self, process_name):
        """通过进程名字获取程序的pid
        Args:
            process_name: 字符串类型，进程名字，例如chrome.exe

        Returns: 没找到返回None,找到了返回pid,并设置self._pid为pid

        """
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == process_name:
                title = self.get_window_titles_by_pid(proc.info['pid'])
                if ("3" in title[0][0]) or ("剑" in title[0][0])and "KGWin32App" in title[0][1]:
                    self._pid = proc.info['pid']

                    return proc.info['pid']
        return None

    def get_window_by_pid(self,pid):
        """获取程序的pid

        Args:
            pid: 程序的pid

        Returns: 找到返回 hwnd并且设置self._hwnd，没招到返回None

        """
        def enum_windows_callback(hwnd, pid_to_check):
            _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
            if window_pid == pid_to_check:
                window_handles.append(hwnd)

        window_handles = []
        win32gui.EnumWindows(enum_windows_callback, pid)
        if window_handles:
            self._hwnd = window_handles[0]
            return window_handles[0]
        return None

    def get_window_screenshot(self,hwnd):
        """获取程序的截图

        Args:
            hwnd: 程序的hwnd

        Returns: 返回对应的程序截图

        """
        with self.lock:
            # 获取客户区的DC，直接抓取客户区内容
            client_dc = win32gui.GetDC(hwnd)
            dc_object = win32ui.CreateDCFromHandle(client_dc)
            compatible_dc = dc_object.CreateCompatibleDC()

            # 获取客户区的位置（相对于整个屏幕）
            client_rect = win32gui.GetClientRect(hwnd)
            left, top, right, bottom = client_rect
            width = right - left
            height = bottom - top

            # 获取窗口相对于屏幕的位置
            # window_rect = win32gui.GetWindowRect(hwnd)
            # app_x, app_y = window_rect[0], window_rect[1]

            # 创建位图对象
            bitmap = win32ui.CreateBitmap()
            bitmap.CreateCompatibleBitmap(dc_object, width, height)
            compatible_dc.SelectObject(bitmap)

            # 抓取客户区内容
            compatible_dc.BitBlt((0, 0), (width, height), dc_object, (left, top), win32con.SRCCOPY)

            # 获取图像数据
            bitmap_bits = bitmap.GetBitmapBits(True)
            image = Image.frombuffer('RGB', (width, height), bitmap_bits, 'raw', 'BGRX', 0, 1)
            np_image = np.array(image)

            # 清理资源
            dc_object.DeleteDC()
            compatible_dc.DeleteDC()
            win32gui.ReleaseDC(hwnd, client_dc)
            win32gui.DeleteObject(bitmap.GetHandle())

            return np_image

    def screenshot_stream(self, hwnd,stop_flag):
        """

        Args:
            hwnd: 程序的hwnd
            stop_flag: 用于外部调用的线程停止标志位

        Returns: 返回一个图片生成器

        """

        while not stop_flag.is_set():  # Check for stop flag
            try:
                screenshot = self.get_window_screenshot(hwnd)
            except Exception as e:
                print(e)

            # 转为 OpenCV 格式
            if screenshot.size == 0:
                return None
            screenshot_cv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

            yield screenshot_cv  # 返回调整后的截图

    def pic(self):
        """用于给主程序生成画面

        Returns:返回图片生成器

        """
        msg = f"{self.case_name}: 展示用例截图"
        print(msg)

        # bounding = sv.BoundingBoxAnnotator()
        bounding = sv.BoxAnnotator()
        lable = sv.LabelAnnotator()

        stream = self.screenshot_stream(self._hwnd, self.stop_flag)  # Assuming this function yields frames
        for frame in stream:
            if frame is None:
                continue

            results = self.model.predict(source=frame, verbose=False)[0]
            detections = sv.Detections.from_ultralytics(results)

            annotated_image = bounding.annotate(
                scene=frame, detections=detections
            )
            annotated_image = lable.annotate(
                scene=annotated_image, detections=detections
            )

            for detection in detections:
                label_name = detection[5]["class_name"]
                x1, y1, x2, y2 = map(int, detection[0][0:4])  # Ensure bounding box coordinates are integers

                confidence = detection[2]  # Confidence score

                text = f'{confidence:.2f}'  # Format confidence to two decimal points
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                color = (0, 255, 0)  # Green color for the confidence text
                thickness = 2

                cv2.putText(annotated_image, text, (x1, y2 + 15), font, font_scale, color, thickness)  # 左下角显示置信度
                cv2.putText(annotated_image, label_name, (x1 + 50, y2 + 15), font, font_scale, color,
                            thickness)  # 左下角显示标签

            # Convert annotated_image from OpenCV (BGR) format to RGB format for QImage
            annotated_image = cv2.cvtColor(annotated_image, cv2.COLOR_RGB2BGR)
            height, width, channel = annotated_image.shape
            bytes_per_line = 3 * width
            q_image = QImage(annotated_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

            yield q_image  # Yield each frame as a QImage for PyQt display

    def right_drag_center(self,hwnd, duration=0.5, speed=10):
        """旋转视角

        Args:
            hwnd: 程序的hwnd
            duration: 旋转界面的时间
            speed: 速度

        Returns:无

        """
        # 获取窗口客户区中心位置
        self.send_key_press(hwnd,"a")
        time.sleep(duration)
        self.send_key_press(hwnd,"F11")

    def send_click(self,hwnd, x, y,img = None,debug = 0):
        """
        向对应的程序发送点击事件
        Args:
            hwnd: 程序的hwnd
            x: x坐标 int
            y: y坐标 int
            img: 图像，用于在开启调试后渲染点击位置
            debug: 是否开启调试，会展示点击位置

        Returns:无

        """
        # Convert x and y to the LPARAM format (x is low-order, y is high-order)
        lparam = win32api.MAKELONG(int(x), int(y))

        # Send the WM_LBUTTONDOWN and WM_LBUTTONUP messages to simulate a click
        win32gui.SendMessage(hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lparam)
        time.sleep(0.5)
        win32gui.SendMessage(hwnd, win32con.WM_LBUTTONUP, None, lparam)

        # Save the image with the marker
        if debug == 1:
            # Draw a red circle at the click location on a copy of the image for verification
            img_with_marker = img.copy()
            cv2.circle(img_with_marker, (int(x), int(y)), 15, (0, 0, 255), -1)  # Red circle, radius 5
            cv2.imshow("click", img_with_marker)

    def send_key_press(self,hwnd, keys):
        """
        向指定程序发送hwnd
        Args:
            hwnd: 程序对应的hwnd
            keys: 按键 字符串类型,支持组合按键例如 ["ctrl","m"] 或者  "F11"

        Returns:无

        """
        # Convert all keys to uppercase

        if isinstance(keys, str):
            keys = [keys.upper()]
        else:
            keys = [key.upper() for key in keys]


        # Send key down events for each key in the combination
        for key in keys:
            if key in self.key_code_map:
                virtual_key = self.key_code_map[key]
                print(f"Sending key '{key}' (VK: {virtual_key}) to window")
                win32gui.SendMessage(hwnd, win32con.WM_KEYDOWN, virtual_key, 0)
            else:
                print(f"Key '{key}' not found in key map!")

        # Send key up events for each key in the combination in reverse order
        for key in reversed(keys):
            if key in self.key_code_map:
                virtual_key = self.key_code_map[key]
                print(f"Releasing key '{key}' (VK: {virtual_key}) from window")
                win32gui.SendMessage(hwnd, win32con.WM_KEYUP, virtual_key, 0)
            else:
                print(f"Key '{key}' not found in key map!")

    def get_ocr(self, img, location, mode=1, debug=0):
        """
        对指定图像进行ocr识别，开启调试后会把灰度图显示出来
        Args:
            img: 图片
            location: int类型 图片的坐标
            mode: int类型 模式选择 1表示cv2.THRESH_TOZERO模式
            debug: int类型 1开启 0关闭，开启后会展示灰度图

        Returns: 存在就返回字符 否则就是None

        """
        x1, y1, x2, y2 = map(int, location)

        cropped_image = img[int(y1):int(y2), int(x1):int(x2)]

        gray_image = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)

        if mode == 1:
            ret, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_TOZERO)
        elif mode == 2:
            ret, thresh_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_TOZERO)

        if debug == 1:
            cv2.imshow("local", gray_image)
            cv2.imshow("gray", thresh_image)
        ocr = PaddleOCR(use_angle_cls=True, lang="ch")

        result = ocr.ocr(thresh_image, cls=True)
        print(result)
        if result[0]:
            return result[0][0][1][0]
        else:
            return None

